"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { PayslipReadOnlyProvider } from "@/providers/payslip-readonly-provider";
import { PayslipNumberInput } from "@/components/payroll/payslip-controls/payslip-number-input";
import { RepeatingControl } from "@/components/payroll/payslip-controls/repeating-control";
import { ZeroizeControl } from "@/components/payroll/payslip-controls/zeroize-control";
import { ShowOnPayslipControl } from "@/components/payroll/payslip-controls/show-on-payslip-control";
import { AddElementButton } from "@/components/payroll/payslip-controls/add-element-button";
import { SectionHeader } from "@/components/payroll/payslip-controls/section-header";
import { RateSelectorControl } from "@/components/payroll/payslip-controls/rate-selector-control";
import { Lock, Unlock } from "lucide-react";

/**
 * Demo component to showcase the read-only functionality
 * This demonstrates how all interactive elements become disabled when payslip status is "closed"
 */
const PayslipReadOnlyDemo: React.FC = () => {
  const [payslipStatus, setPayslipStatus] = useState<"open" | "closed">("open");
  const [demoValue, setDemoValue] = useState<number>(1000);
  const [isRepeating, setIsRepeating] = useState<boolean>(false);
  const [isZeroize, setIsZeroize] = useState<boolean>(false);
  const [showOnPayslip, setShowOnPayslip] = useState<boolean>(true);
  const [currentRate, setCurrentRate] = useState<number>(20.0);

  const toggleStatus = () => {
    setPayslipStatus(payslipStatus === "open" ? "closed" : "open");
  };

  const handleAddElement = () => {
    console.log("Add element clicked");
  };

  const handleClearValues = () => {
    console.log("Clear values clicked");
  };

  const handleDeleteAll = () => {
    console.log("Delete all clicked");
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Payslip Read-Only Demo</h1>
        <Button onClick={toggleStatus} variant="outline">
          {payslipStatus === "open" ? (
            <>
              <Lock className="mr-2 h-4 w-4" />
              Close Payslip
            </>
          ) : (
            <>
              <Unlock className="mr-2 h-4 w-4" />
              Reopen Payslip
            </>
          )}
        </Button>
      </div>

      <div className="flex items-center gap-3">
        <span className="text-lg font-medium">Current Status:</span>
        <div
          className={`flex items-center gap-2 rounded-md px-3 py-1 ${
            payslipStatus === "closed"
              ? "bg-slate-100 dark:bg-slate-800"
              : "bg-green-100 dark:bg-green-900"
          }`}
        >
          {payslipStatus === "closed" ? (
            <Lock className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          ) : (
            <Unlock className="h-4 w-4 text-green-600 dark:text-green-400" />
          )}
          <span
            className={`text-sm font-medium ${
              payslipStatus === "closed"
                ? "text-slate-600 dark:text-slate-400"
                : "text-green-600 dark:text-green-400"
            }`}
          >
            {payslipStatus === "closed" ? "Closed" : "Open"}
          </span>
        </div>
      </div>

      <PayslipReadOnlyProvider payslipStatus={payslipStatus}>
        <Card className="space-y-6 p-6">
          <SectionHeader
            title="Demo Section"
            sectionType="basic"
            addButtons={[
              { label: "Add Element", onClick: handleAddElement },
              { label: "Add Another", onClick: handleAddElement },
            ]}
            actionButtons={[
              {
                label: "Clear Values",
                onClick: handleClearValues,
                variant: "outline",
              },
              {
                label: "Delete All",
                onClick: handleDeleteAll,
                variant: "destructive",
              },
            ]}
          />

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Number Input</h3>
              <PayslipNumberInput
                value={demoValue}
                onChange={setDemoValue}
                currencySymbol="£"
                className="w-full"
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Control Checkboxes</h3>
              <div className="flex items-center gap-4">
                <RepeatingControl
                  id="demo-repeating"
                  isChecked={isRepeating}
                  onChange={setIsRepeating}
                />
                <ZeroizeControl
                  id="demo-zeroize"
                  isChecked={isZeroize}
                  onChange={setIsZeroize}
                  disabled={!isRepeating}
                />
                <ShowOnPayslipControl
                  id="demo-show"
                  isChecked={showOnPayslip}
                  onChange={setShowOnPayslip}
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Rate Selector</h3>
              <div className="flex items-center gap-2">
                <span className="text-sm">
                  Current Rate: £{currentRate.toFixed(2)}
                </span>
                <RateSelectorControl
                  id="demo-rate"
                  currentRate={currentRate}
                  onChange={(rate) => setCurrentRate(rate)}
                  standardRate={20.0}
                  savedRates={[
                    { label: "Standard", value: 20.0 },
                    { label: "Overtime", value: 30.0 },
                  ]}
                />
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Add Element Buttons</h3>
              <div className="flex gap-2">
                <AddElementButton
                  label="Basic Element"
                  onClick={handleAddElement}
                  sectionType="basic"
                />
                <AddElementButton
                  label="Addition"
                  onClick={handleAddElement}
                  sectionType="additions"
                />
                <AddElementButton
                  label="Deduction"
                  onClick={handleAddElement}
                  sectionType="deductions"
                />
              </div>
            </div>
          </div>

          <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>Instructions:</strong> Click the "Close Payslip" button
              above to see how all interactive elements become disabled and
              visually muted when the payslip status is "closed". The lock icon
              will appear and all controls will be non-interactive.
            </p>
          </div>
        </Card>
      </PayslipReadOnlyProvider>
    </div>
  );
};

export default PayslipReadOnlyDemo;
